#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有修复后的功能
"""

import sys
from pathlib import Path
from docx_to_latex_correct import CorrectDocxToLatexConverter

def test_all_fixes():
    """测试所有修复后的功能"""
    print("🧪 测试所有修复后的功能...")
    
    # 测试文档
    test_docx = Path("实例文档/双栏案列/FLS 8565 - 原始版本.docx")
    
    if not test_docx.exists():
        print(f"❌ 找不到测试文档: {test_docx}")
        return False
    
    # 创建输出目录
    output_dir = Path("test_all_fixes_output")
    
    try:
        # 转换文档
        print("📝 开始转换文档...")
        converter = CorrectDocxToLatexConverter(str(test_docx), str(output_dir))
        converter.convert_to_latex()
        
        # 检查输出文件
        output_tex = output_dir / "output.tex"
        images_dir = output_dir / "images"
        output_pdf = output_dir / "output.pdf"
        
        if not output_tex.exists():
            print("❌ LaTeX文件未生成")
            return False
            
        print("✅ LaTeX文件生成成功")
        
        # 检查图片文件
        if images_dir.exists():
            image_files = list(images_dir.glob("figure*.png"))
            print(f"📁 找到 {len(image_files)} 个图片文件")
        else:
            print("❌ 图片目录未创建")
            
        # 检查PDF文件
        if output_pdf.exists():
            pdf_size = output_pdf.stat().st_size
            print(f"📄 PDF文件生成成功: {pdf_size:,} 字节")
        else:
            print("❌ PDF文件未生成")
            
        # 检查LaTeX文件内容
        with open(output_tex, 'r', encoding='utf-8') as f:
            tex_content = f.read()
            
        # 验证关键修复
        checks = {
            "表格环境修复": r'\\begin{table}[H]',
            "表格不使用table*": r'\\begin{table\*}',
            "图片引用": r'\\includegraphics.*images/figure',
            "表格引用": r'\\begin{tabularx}',
            "双栏环境": r'\\begin{multicols}{2}',
            "官方宏包flushend": r'\\usepackage{flushend}',
            "官方宏包balance": r'\\usepackage{balance}',
            "官方宏包tabularx": r'\\usepackage{tabularx}',
            "官方宏包booktabs": r'\\usepackage{booktabs}',
            "正确的引用格式": r'\\setcitestyle{super,open=\[,close=\]',
        }
        
        results = {}
        for check_name, pattern in checks.items():
            import re
            if check_name == "表格不使用table*":
                # 这个应该不存在
                matches = re.findall(pattern, tex_content)
                results[check_name] = len(matches) == 0
            else:
                matches = re.findall(pattern, tex_content)
                results[check_name] = len(matches) > 0
                
        print("\n🔍 修复验证结果:")
        all_passed = True
        for check_name, passed in results.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
                
        # 统计图片和表格
        import re
        figure_refs = re.findall(r'\\includegraphics.*images/figure\d+\.png', tex_content)
        table_refs = re.findall(r'\\begin{table}', tex_content)
        
        print(f"\n📊 内容统计:")
        print(f"   📷 图片引用: {len(figure_refs)} 个")
        print(f"   📋 表格: {len(table_refs)} 个")
        
        if all_passed:
            print("\n🎉 所有修复验证通过！")
            return True
        else:
            print("\n⚠️ 部分修复需要进一步检查")
            return False
            
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_all_fixes()
    sys.exit(0 if success else 1)
