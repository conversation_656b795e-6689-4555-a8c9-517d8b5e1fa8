\documentclass[11pt]{article}
\usepackage{graphicx}
\usepackage[paperwidth=210mm, paperheight=285mm, top=2.5cm, bottom=2.5cm, left=2cm, right=2cm]{geometry}
\usepackage{multicol}
\usepackage{booktabs}
\usepackage{tabularx}
\usepackage{float}
\usepackage{caption}

\renewcommand{\arraystretch}{1}
\newcolumntype{C}{>{\centering\arraybackslash}X}
\setlength{\abovecaptionskip}{3pt}
\setlength{\belowcaptionskip}{0pt}
\captionsetup{
    font={footnotesize},
    justification=centering,
    labelfont=bf,
    textfont=normalfont
}

\begin{document}

\begin{multicols}{2}

This is some text before the table.

\vspace{-8pt}
\begin{table}[H]
\captionsetup{justification=raggedright,singlelinecheck=false}
\caption{Test single column table}
\vspace{3pt}
\small
\tabcolsep=0.88cm
\begin{tabular}{c}
\toprule[0.5pt]
\textbf{Test Header} \\
\midrule[0.25pt]
Item 1 \\
Item 2 \\
Item 3 \\
\bottomrule[0.5pt]
\end{tabular}
\end{table}

This is some text after the table.

\end{multicols}

\end{document}
