# 转换程序与官方模板差异分析

## 1. 宏包引用差异

### ❌ 我们缺少的关键宏包：
```latex
% 官方有，我们没有：
\usepackage{flushend}           % 双栏平衡 - 重要！
\usepackage{balance}            % 双栏平衡 - 重要！
\usepackage{changepage}         % 页面调整
\usepackage[mathlines]{lineno}  % 行号
\usepackage{bm}                 % 数学粗体
\usepackage{titling}            % 标题设置
\usepackage{lipsum}             % 示例文本
\usepackage{setspace}           % 行间距
\usepackage{microtype}          % 微调排版
\usepackage{titletoc}           % 目录格式
\usepackage{url}                % URL处理 - 重要！
\usepackage{tabularx}           % 表格通栏 - 重要！
\usepackage{multirow}           % 多行表格 - 重要！
\usepackage{float}              % 浮动体控制 - 重要！
\usepackage{makecell}           % 表格单元格 - 重要！
\usepackage{caption}            % 标题设置 - 重要！
\usepackage{booktabs}           % 表格线条 - 重要！
\usepackage{placeins}           % 浮动体位置
```

### ❌ 我们多余的宏包：
```latex
% 我们有，官方没有：
无明显多余宏包
```

### ❌ 配置差异：
```latex
% 官方有的配置，我们缺少：
\def\UrlBreaks{...}             % URL断行配置 - 重要！
\renewcommand{\arraystretch}{1} % 表格行距
\newcolumntype{C}{>{\centering\arraybackslash}X} % C列类型定义
\setlength{\abovecaptionskip}{3pt}  % 标题上方距离
\setlength{\belowcaptionskip}{0pt}  % 标题下方距离
```

## 2. 标题格式设置差异

### ❌ 我们缺少的标题设置：
```latex
% 官方的完整标题设置，我们缺少：
\captionsetup{
    font={footnotesize},        % 9pt字体
    justification=centering,    % 居中对齐
    labelfont=bf,              % 标签粗体
    textfont=normalfont        % 文字正常
}

% 标题格式定义
\titleformat{\section}
  {\fontsize{15pt}{18}\bfseries\selectfont}
  {\thesection.}
  {0.5em}
  {}
\titlespacing*{\section}{0pt}{15pt}{10pt}

\titleformat{\subsection}
  {\color{black}\bfseries\fontsize{12}{14.4}\selectfont}
  {\thesubsection.}
  {0.5em}
  {}
\titlespacing*{\subsection}{0pt}{15pt}{10pt}

\titleformat{\subsubsection}
  {\color{black}\bfseries\fontsize{12}{14.4}\selectfont}
  {\thesubsubsection.}
  {0.5em}
  {}
\titlespacing*{\subsubsection}{0pt}{5pt}{5pt}
```

## 3. 页眉页脚设置差异

### ❌ 我们的页眉设置不完整：

#### 官方的完整页眉设置：
```latex
% 首页页眉
\fancypagestyle{first}{%
  \fancyhf{}
  \renewcommand{\headrulewidth}{0.75pt}
  \renewcommand{\headrule}{\color{black}\hrule width\headwidth height \headrulewidth}
  \fancyhead[C]{
    \par\vspace{0.6pt}
    {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Forum for Linguistic Studies}} |  Volume 07 | Issue 07 | July 2025}}
    \par\vspace{-12pt}
  }
  \setlength{\headsep}{4mm}
  \fancyfoot[C]{\thepage}
  \renewcommand{\footrulewidth}{0pt}
}

% 其他页页眉
\fancypagestyle{other}{%
    \fancyhf{}
    \renewcommand{\headrulewidth}{0.75pt}
    \renewcommand{\headrule}{\color{black}\hrule width\headwidth height \headrulewidth}
    \fancyhead[C]{\par\vspace{0.6pt}
    {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Forum for Linguistic Studies}} | Volume 07 | Issue 07 | July 2025}} \par\vspace{-12pt}
    }
    \setlength{\headsep}{7mm}
    \setcounter{page}{1071}
    \fancyfoot[C]{\thepage}
    \renewcommand{\footrulewidth}{0pt}
}
```

#### 我们的页眉设置问题：
- 缺少 `\renewcommand{\headrule}` 设置
- 缺少精确的间距控制
- 缺少页码起始值设置

## 4. 参考文献格式差异

### ❌ 引用格式差异：
```latex
% 官方：
\setcitestyle{super,open=[,close=],citesep={, }}

% 我们：
\setcitestyle{super,open=,close=,citesep={,}}
```

## 5. 表格处理的关键差异

### ❌ 我们的表格环境错误：
```latex
% 我们错误的做法：
\end{multicols}
\begin{table*}[H]  % ❌ 错误：使用table*
\begin{tabularx}{\textwidth}{CC}
...
\end{tabularx}
\end{table*}
\begin{multicols}{2}

% 官方正确的做法：
\begin{table}[H]   % ✅ 正确：使用table
\begin{tabularx}{\textwidth}{CCCC}
...
\end{tabularx}
\end{table}
```

### ❌ 表格标题设置差异：
```latex
% 官方的表格标题设置：
\captionsetup{justification=raggedright,singlelinecheck=false}
\caption{表格标题}
\vspace{3pt}
\small
\tabcolsep=0.88cm  % 列间距设置

% 我们缺少：
- tabcolsep设置
- 正确的标题对齐方式
```

## 6. 图片处理差异

### ❌ 图片标题设置差异：
```latex
% 官方的图片标题设置：
\captionsetup{labelfont=bf, labelsep=period, justification=raggedright}

% 跨栏图片的标题设置：
\captionsetup{justification=justified,singlelinecheck=false}

% 我们的设置不够精确
```

## 7. 多栏环境控制差异

### ❌ 我们的多栏控制过于复杂：
```latex
% 我们的做法（过于复杂）：
\end{multicols}
% 跨栏内容
\begin{multicols}{2}

% 官方的做法（更简洁）：
% 在双栏环境内直接使用tabularx跨栏
% 只在必要时跳出多栏环境
```

## 8. 缺少的重要配置

### ❌ URL断行配置：
```latex
\def\UrlBreaks{\do\A\do\B\do\C\do\D\do\E\do\F\do\G\do\H\do\I\do\J
\do\K\do\L\do\M\do\N\do\O\do\P\do\Q\do\R\do\S\do\T\do\U\do\V
\do\W\do\X\do\Y\do\Z\do\[\do\\\do\]\do\^\do\_\do\`\do\a\do\b
\do\c\do\d\do\e\do\f\do\g\do\h\do\i\do\j\do\k\do\l\do\m\do\n
\do\o\do\p\do\q\do\r\do\s\do\t\do\u\do\v\do\w\do\x\do\y\do\z
\do\.\do\@\do\\\do\/\do\!\do\_\do\|\do\;\do\>\do\]\do\)\do\,
\do\?\do\'\do+\do\=\do\#}
```

### ❌ 列表格式配置：
```latex
\setitemize{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}
\setenumerate{topsep=3pt,parsep=0pt,itemsep=0pt,leftmargin=*,labelsep=5.5mm,align=parleft}
\setlist[description]{itemsep=0mm}
```

## 9. 需要修复的优先级

### 🔥 高优先级（影响表格显示）：
1. 添加缺少的表格相关宏包
2. 修复表格环境使用错误
3. 添加表格配置设置

### 🔥 中优先级（影响格式）：
1. 添加缺少的宏包
2. 完善页眉页脚设置
3. 修复标题格式设置

### 🔥 低优先级（优化细节）：
1. 添加URL断行配置
2. 完善列表格式
3. 优化多栏控制逻辑

## 总结

我们的转换程序与官方模板有**重大差异**，特别是：
1. **表格环境使用完全错误**（这是表格不显示的根本原因）
2. **缺少关键宏包**（影响功能）
3. **配置设置不完整**（影响格式）
4. **页眉页脚设置简化**（影响外观）

需要进行**全面修复**才能完全符合官方模板要求。
