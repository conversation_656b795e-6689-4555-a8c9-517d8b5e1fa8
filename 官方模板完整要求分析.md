# 官方LaTeX模板完整要求分析

## 1. 文档类和基本设置

### 官方要求：
```latex
\documentclass[11pt]{article}
```

### 关键宏包（按顺序）：
```latex
\usepackage{flushend}           % 双栏平衡
\usepackage{graphicx}           % 图片支持
\usepackage{balance}            % 双栏平衡（重复）
\usepackage[paperwidth=210mm, paperheight=285mm, top=2.5cm, bottom=2.5cm, left=2cm, right=2cm]{geometry}
\usepackage{fancyhdr}           % 页眉页脚
\usepackage[absolute, overlay]{textpos}
\usepackage{subcaption}         % 子图支持
\usepackage{amsmath}            % 数学公式
\usepackage{amssymb}            % 数学符号
\usepackage{amsfonts}           % 数学字体
\usepackage{amsthm, bm}         % 数学定理
\usepackage{changepage}
\usepackage[mathlines]{lineno}
\usepackage{xcolor}             % 颜色支持
\usepackage{tcolorbox}          % 彩色框
\usepackage{multicol}           % 多栏支持 !!!
\usepackage{etoolbox}           % 参考文献自定义
\usepackage{enumitem}           % 列表格式
\usepackage{ragged2e}           % 对齐设置
\usepackage{titlesec}           % 标题自定义
\usepackage{fontspec}           % 字体设置
\usepackage{parskip}            % 段落间距
\usepackage{titling}
\usepackage{setspace}           % 行间距
\usepackage[numbers,sort&compress]{natbib}  % 参考文献
\usepackage{indentfirst}        % 首行缩进
\usepackage{microtype}
\usepackage{titletoc}
\usepackage[colorlinks,linkcolor=black,urlcolor=black,citecolor=black]{hyperref}
\usepackage{url}                % URL断行
\usepackage{tabularx}           % 表格通栏显示 !!!
\usepackage{multirow}           % 多行表格
\usepackage{float}              % 浮动体控制
\usepackage{makecell}           % 表格单元格
\usepackage{caption}            % 标题设置
\usepackage{booktabs}           % 表格线条
\usepackage{placeins}           % 浮动体位置
```

## 2. 关键配置设置

### 表格配置：
```latex
\renewcommand{\arraystretch}{1}                    % 表格行距
\newcolumntype{C}{>{\centering\arraybackslash}X}   % 定义C列类型
\setlength{\abovecaptionskip}{3pt}                 % 标题上方距离
\setlength{\belowcaptionskip}{0pt}                 % 标题下方距离
```

### 标题配置：
```latex
\captionsetup{
    font={footnotesize},        % 9pt字体
    justification=centering,    % 居中对齐
    labelfont=bf,              % 标签粗体
    textfont=normalfont        % 文字正常
}
```

### 字体和段落：
```latex
\setmainfont{Times New Roman}   % 主字体
\setlength{\parindent}{2em}     % 首行缩进2字符
\setlength{\parskip}{0pt}       % 段落间距0
```

### 参考文献：
```latex
\apptocmd{\thebibliography}{\small}{}{}
\setcitestyle{super,open=[,close=],citesep={, }}
\bibliographystyle{plain}
```

## 3. 页眉页脚设置

### 首页页眉：
```latex
\fancypagestyle{first}{%
  \fancyhf{}
  \renewcommand{\headrulewidth}{0.75pt}
  \fancyhead[C]{
    \par\vspace{0.6pt}
    {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Forum for Linguistic Studies}} |  Volume 07 | Issue 07 | July 2025}}
    \par\vspace{-12pt}
  }
  \setlength{\headsep}{4mm}     % 首页页眉间距
  \fancyfoot[C]{\thepage}
}
```

### 其他页页眉：
```latex
\fancypagestyle{other}{%
    \fancyhf{}
    \renewcommand{\headrulewidth}{0.75pt}
    \fancyhead[C]{\par\vspace{0.6pt}
    {\noindent\fontsize{9pt}{10pt}\selectfont{ \textit{\textbf{Forum for Linguistic Studies}} | Volume 07 | Issue 07 | July 2025}} \par\vspace{-12pt}
    }
    \setlength{\headsep}{7mm}     % 其他页页眉间距
    \setcounter{page}{1071}       % 页码起始值
    \fancyfoot[C]{\thepage}
}
```

## 4. 标题格式设置

### 一级标题：
```latex
\titleformat{\section}
  {\fontsize{15pt}{18}\bfseries\selectfont}
  {\thesection.}
  {0.5em}
  {}
\titlespacing*{\section}{0pt}{15pt}{10pt}
```

### 二级标题：
```latex
\titleformat{\subsection}
  {\color{black}\bfseries\fontsize{12}{14.4}\selectfont}
  {\thesubsection.}
  {0.5em}
  {}
\titlespacing*{\subsection}{0pt}{15pt}{10pt}
```

### 三级标题：
```latex
\titleformat{\subsubsection}
  {\color{black}\bfseries\fontsize{12}{14.4}\selectfont}
  {\thesubsubsection.}
  {0.5em}
  {}
\titlespacing*{\subsubsection}{0pt}{5pt}{5pt}
```

## 5. 图片格式要求

### 单栏图片（在双栏环境内）：
```latex
\begin{figure}[H]
\centering
\label{Figure 1.}
\includegraphics[width=0.25\linewidth]{image1.png}
\vspace{6pt}
\captionsetup{labelfont=bf, labelsep=period, justification=raggedright}
\caption{This is a figure. Schemes follow the same formatting.}
\end{figure}
```

### 跨栏图片（跳出双栏环境）：
```latex
\end{multicols}

\vspace{-6pt}
\begin{figure}[H]
\centering
\label{Figure 2.}
\begin{tabular}{cc}
\includegraphics[width=0.25\linewidth]{image2a.png}&\includegraphics[width=0.25\linewidth]{image2b.png}\\
(\textbf{a})&(\textbf{b})
\end{tabular}
\vspace{6pt}
\captionsetup{justification=justified,singlelinecheck=false}
\caption{多面板图片描述...}
\end{figure}

\begin{multicols}{2}
```

## 6. 表格格式要求

### 单栏表格（在双栏环境内）：
```latex
\vspace{-8pt}
\begin{table}[H]
\captionsetup{justification=raggedright,singlelinecheck=false}
\caption{This is a table. Tables should be placed in the main text near to the first time they are cited.\label{tab1}}
\vspace{3pt}
\small
\tabcolsep=0.88cm
\begin{tabular}{ccc}
\toprule[0.5pt]
\textbf{Title 1} & \textbf{Title 2} & \textbf{Title 3} \\
\midrule[0.25pt]
entry 1 & data & data \\
entry 2 & data & data $^{1}$ \\
\bottomrule[0.5pt]
\end{tabular}
 \vspace{1mm}
 \parbox{\textwidth}{\fontsize{7pt}{8}\selectfont{\textsuperscript{1} Tables may have a footer.}}
\end{table}
```

### 跨栏表格（在双栏环境外）：
```latex
\vspace{-8pt}
\begin{table}[H]
\caption{This is a table. Tables should be placed in the main text near to the first time they are cited.\label{tab2}}
\small
\newcolumntype{C}{>{\centering\arraybackslash}X}
\begin{tabularx}{\textwidth}{CCCC}
\toprule[0.5pt]
\multicolumn{2}{c}{\textbf{Title 1}} & \multicolumn{2}{c}{\textbf{Title 2}}\\
\midrule[0.25pt]
\textbf{Title 3}& \textbf{Title 4}& \textbf{Title 5}& \textbf{Title 6}\\
\midrule[0.25pt]
\multirow[m]{3}{*}{Entry 1 *} & Data & Data & Data\\
    & Data & Data & Data\\
  & Data & Data & Data\\
\bottomrule[0.5pt]
\end{tabularx}
 \vspace{1mm}
 \parbox{\textwidth}{\fontsize{7pt}{8}\selectfont{\textsuperscript{1} Tables may have a footer.}}
\end{table}
```

## 7. 多栏环境使用

### 正确的多栏控制：
```latex
\begin{multicols}{2}
% 双栏内容

% 跨栏元素需要跳出多栏环境
\end{multicols}

% 跨栏图片或表格

\begin{multicols}{2}
% 继续双栏内容
\end{multicols}
```

## 8. 关键发现

### ❌ 错误做法（我们当前的问题）：
1. 使用 `\begin{table*}[H]` 环境
2. 手动控制 `\end{multicols}` 和 `\begin{multicols}{2}`
3. 缺少关键宏包如 `flushend`, `balance`
4. 页眉设置不完整
5. 标题格式不符合要求

### ✅ 正确做法（官方要求）：
1. 使用 `\begin{table}[H]` + `\begin{tabularx}`
2. 让多栏环境自然控制跨栏
3. 包含所有必需宏包
4. 完整的页眉页脚设置
5. 精确的标题和间距设置
