#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PDF内容中是否包含表格
"""

import PyPDF2
from pathlib import Path

def extract_pdf_text(pdf_path):
    """提取PDF文本"""
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        text = ""
        for page_num, page in enumerate(reader.pages):
            text += f"\n=== 第{page_num + 1}页 ===\n"
            text += page.extract_text()
    return text

def check_tables_in_pdf():
    """检查PDF中的表格内容"""
    pdf_path = Path("test_output/output.pdf")
    
    if not pdf_path.exists():
        print("❌ PDF文件不存在")
        return
    
    print("🔍 提取PDF文本内容...")
    text = extract_pdf_text(pdf_path)
    
    # 检查表格相关关键词
    table_keywords = [
        "Table 1", "Table 2", "Table 3", "Table 4",
        "Buriram Airport staff",
        "shop owners",
        "van and taxi drivers", 
        "evaluation of English communication",
        "Topics and Content",
        "Greeting and Welcoming",
        "Format",
        "Content",
        "English audio"
    ]
    
    print("\n📋 检查表格相关内容:")
    found_keywords = []
    for keyword in table_keywords:
        if keyword.lower() in text.lower():
            found_keywords.append(keyword)
            print(f"✅ 找到: {keyword}")
        else:
            print(f"❌ 未找到: {keyword}")
    
    print(f"\n📊 总结: 找到 {len(found_keywords)}/{len(table_keywords)} 个表格相关关键词")
    
    # 保存提取的文本到文件
    with open("test_output/extracted_text.txt", "w", encoding="utf-8") as f:
        f.write(text)
    print("💾 PDF文本已保存到: test_output/extracted_text.txt")

if __name__ == "__main__":
    check_tables_in_pdf()
